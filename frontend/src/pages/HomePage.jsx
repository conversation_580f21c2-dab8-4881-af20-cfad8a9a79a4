import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useBooking } from '../context/BookingContext';
import AirportAutocomplete from '../components/AirportAutocomplete';
import Testimonials from '../components/Testimonials';
import AnimatedCounter from '../components/AnimatedCounter';


import FloatingCTA from '../components/FloatingCTA';
import ValidationSummary from '../components/ValidationSummary';
import LoadingSpinner from '../components/LoadingSpinner';

import InlineFlightResults from '../components/InlineFlightResults';
import ErrorBoundary from '../components/ErrorBoundary';



import { validateDate, validateReturnDate, validateAirportSelection } from '../utils/validation';
import { flightAPI, healthCheck } from '../services/api';
import { saveCheckoutData } from '../utils/sessionStorageHelper';

const HomePage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    setSearchData: setBookingSearchData,
    resetBooking,
    tripType,
    setTripType,
    setSelectedFlight,
    setSelectedOutboundFlight,
    setSelectedReturnFlight,
    selectedOutboundFlight,
    selectedReturnFlight,
    passengers,
    setPassengers,
    email,
    setEmail
  } = useBooking();



  const [searchData, setSearchData] = useState({
    origin: '',
    destination: '',
    date: '',
    returnDate: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  // Inline results state
  const [showResults, setShowResults] = useState(false);
  const [outboundFlights, setOutboundFlights] = useState([]);
  const [returnFlights, setReturnFlights] = useState([]);
  const [oneWayFlights, setOneWayFlights] = useState([]);
  const [searchError, setSearchError] = useState(null);
  const [hasSearched, setHasSearched] = useState(false);

  // New state for inline passenger details
  const [showPassengerDetails, setShowPassengerDetails] = useState(false);

  // Edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [editModeMessage, setEditModeMessage] = useState('');

  const [isPassengerDetailsLoading, setIsPassengerDetailsLoading] = useState(false);

  // Ref for smooth scrolling to results
  const resultsRef = useRef(null);
  const searchFormRef = useRef(null);
  const passengerDetailsRef = useRef(null);

  // Handle edit mode from checkout
  useEffect(() => {
    if (location.state?.editMode && location.state?.preservedData) {
      console.log('✅ HomePage: Edit mode detected, restoring data');
      setIsEditMode(true);
      setEditModeMessage(location.state.message || 'Edit your booking details below. Your progress has been saved.');

      const preservedData = location.state.preservedData;

      // Restore search form data
      setSearchData({
        origin: preservedData.from || '',
        destination: preservedData.to || '',
        date: preservedData.departureDate || '',
        returnDate: preservedData.returnDate || ''
      });

      // Restore trip type
      if (preservedData.tripType) {
        setTripType(preservedData.tripType);
      }

      // Restore passenger data
      if (preservedData.passengers) {
        setPassengers(preservedData.passengers);
      }

      // If coming from edit details button, show passenger details directly
      if (location.state.showPassengerDetailsDirectly) {
        console.log('✅ HomePage: Showing passenger details directly from edit flow');
        setShowResults(true);
        setHasSearched(true);
        setShowPassengerDetails(true);

        // Scroll to passenger details section after a brief delay
        setTimeout(() => {
          const passengerSection = document.querySelector('[data-passenger-details]');
          if (passengerSection) {
            passengerSection.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            });
          }
        }, 500);
      }

      if (preservedData.email) {
        setEmail(preservedData.email);
      }

      // Show the search form and scroll to it
      setTimeout(() => {
        if (searchFormRef.current) {
          searchFormRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }, 100);

      // Clear the location state to prevent re-triggering
      window.history.replaceState({}, document.title);
    }
  }, [location.state, setTripType, setPassengers, setEmail]);



  const validateForm = () => {
    console.log('🔍 validateForm: Starting validation with searchData:', searchData);
    const errors = {};

    // Validate airport selection
    console.log('🔍 Validating airports:', { origin: searchData.origin, destination: searchData.destination });
    const airportErrors = validateAirportSelection(searchData.origin, searchData.destination);
    console.log('🔍 Airport validation results:', airportErrors);
    Object.assign(errors, airportErrors);

    // Validate departure date
    console.log('🔍 Validating departure date:', searchData.date);
    const dateError = validateDate(searchData.date, 'Departure date');
    if (dateError) {
      console.log('❌ Date validation failed:', dateError);
      errors.date = dateError;
    } else {
      console.log('✅ Date validation passed');
    }

    // Validate return date for return trips
    if (tripType === 'return') {
      console.log('🔍 Validating return date:', searchData.returnDate);
      const returnDateError = validateReturnDate(searchData.date, searchData.returnDate);
      if (returnDateError) {
        console.log('❌ Return date validation failed:', returnDateError);
        errors.returnDate = returnDateError;
      } else {
        console.log('✅ Return date validation passed');
      }
    }

    console.log('🔍 Final validation results:', { errors, isValid: Object.keys(errors).length === 0 });
    setValidationErrors(errors);
    const isValid = Object.keys(errors).length === 0;
    return isValid;
  };



  const handleSearch = async (e) => {
    e.preventDefault();

    // Clear previous errors and results
    setValidationErrors({});
    setSearchError(null);
    setOneWayFlights([]);
    setOutboundFlights([]);
    setReturnFlights([]);

    // Validate form
    if (!validateForm()) {
      setShowResults(true);
      return;
    }

    setIsLoading(true);
    setHasSearched(true);

    // Clear previous flight selections
    setSelectedOutboundFlight(null);
    setSelectedReturnFlight(null);

    try {
      // Prepare search data
      const searchParams = {
        origin: searchData.origin,
        destination: searchData.destination,
        date: searchData.date,
        returnDate: tripType === 'return' ? searchData.returnDate : null,
        tripType: tripType
      };

      console.log('🔍 HomePage: Starting flight search with params:', searchParams);

      // Enhanced validation with better error messages
      if (!searchParams.origin || !searchParams.destination || !searchParams.date) {
        console.error('❌ Missing required parameters:', { origin: searchParams.origin, destination: searchParams.destination, date: searchParams.date });
        throw new Error('Missing required search parameters');
      }

      // Validate IATA codes format
      console.log('🔍 Validating IATA codes:', { origin: searchParams.origin, destination: searchParams.destination });
      if (!/^[A-Z]{3}$/.test(searchParams.origin)) {
        console.error('❌ Invalid origin IATA code:', searchParams.origin);
        throw new Error('Please select a valid origin airport from the dropdown');
      }

      if (!/^[A-Z]{3}$/.test(searchParams.destination)) {
        console.error('❌ Invalid destination IATA code:', searchParams.destination);
        throw new Error('Please select a valid destination airport from the dropdown');
      }

      console.log('✅ Validation passed, proceeding with search...');

      // Set search data in context for later use
      setBookingSearchData(searchParams);

      // Test backend connection first
      console.log('🔍 Testing backend connection...');
      try {
        await healthCheck();
        console.log('✅ Backend connection successful');
      } catch (healthError) {
        console.error('❌ Backend health check failed:', healthError);
        throw new Error('Flight search service is currently offline. Please try again later.');
      }

      // Call flight search API with timeout handling
      console.log('🌐 Making API call to flight search service...');
      const response = await flightAPI.searchFlights(searchParams);
      console.log('✅ HomePage: Flight search response received:', response);

      if (response && response.success) {
        const { data } = response;

        if (data.tripType === 'return') {
          // Handle return flight response
          setOutboundFlights(data.outboundFlights || []);
          setReturnFlights(data.returnFlights || []);
          setOneWayFlights([]);

          if ((data.outboundFlights?.length || 0) === 0 || (data.returnFlights?.length || 0) === 0) {
            setSearchError('No flights found for your search criteria. Please try different airports or dates.');
          } else {
            setSearchError(null);
          }
        } else {
          // Handle one-way flight response
          if (Array.isArray(data.flights)) {
            setOneWayFlights(data.flights);
            setOutboundFlights([]);
            setReturnFlights([]);

            if (data.flights.length === 0) {
              setSearchError('No flights found for your search criteria. Please try different airports or dates.');
            } else {
              setSearchError(null);
            }
          } else {
            setSearchError('Invalid flight data received from server');
          }
        }

        // Show results and scroll to them
        setShowResults(true);
        setHasSearched(true);
        setTimeout(() => {
          resultsRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }, 100);

      } else {
        setSearchError(response?.message || 'No flights found for your search criteria');
        setShowResults(true);
      }

    } catch (error) {
      console.error('❌ HomePage: Flight search error:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        code: error.code
      });

      // Provide user-friendly error messages with better specificity
      let errorMessage = 'Flight search service is temporarily unavailable. Please try again in a few moments.';

      // Handle validation errors first
      if (error.message?.includes('select a valid') || error.message?.includes('dropdown')) {
        errorMessage = error.message;
      }
      // Check if backend provided a specific error message
      else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      // Handle specific HTTP status codes
      else if (error.response?.status === 503) {
        errorMessage = 'Flight search service is temporarily unavailable. Please try again later.';
      } else if (error.response?.status === 408) {
        errorMessage = 'Search took too long. Please try again with different criteria.';
      } else if (error.response?.status === 500) {
        errorMessage = 'Flight search service is temporarily unavailable. Please try again in a few moments.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Flight search service not found. Please contact support if this persists.';
      }
      // Handle network and connection errors
      else if (error.message?.includes('Network Error') || error.code === 'NETWORK_ERROR') {
        errorMessage = 'Unable to connect to flight search service. Please check your internet connection and try again.';
      } else if (error.message?.includes('timeout') || error.code === 'ECONNABORTED') {
        errorMessage = 'Flight search is taking longer than expected. Please try again.';
      }
      // Handle connection refused (backend not running)
      else if (error.code === 'ECONNREFUSED' || error.message?.includes('ECONNREFUSED')) {
        errorMessage = 'Flight search service is currently offline. Please try again later.';
      }

      setSearchError(errorMessage);
      setShowResults(true);

      // Clear any partial flight data to prevent rendering issues
      setOneWayFlights([]);
      setOutboundFlights([]);
      setReturnFlights([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    console.log(`🔍 handleInputChange: ${field} = "${value}" (type: ${typeof value})`);

    setSearchData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };
      console.log('🔍 Updated searchData:', newData);
      return newData;
    });

    // Clear validation error for this field when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }

    // Special handling for return date validation when departure date changes
    if (field === 'date' && tripType === 'return' && searchData.returnDate) {
      if (validationErrors.returnDate && value < searchData.returnDate) {
        setValidationErrors(prev => ({
          ...prev,
          returnDate: null
        }));
      }
    }

    // Clear flight selections when search criteria changes
    // This ensures users don't accidentally proceed with flights from a previous search
    if (hasSearched && (field === 'origin' || field === 'destination' || field === 'date' || field === 'returnDate')) {
      setSelectedOutboundFlight(null);
      setSelectedReturnFlight(null);
      setShowPassengerDetails(false);
    }
  };

  // Flight selection handlers with toggle functionality
  const handleOneWayFlightSelect = (flight) => {
    // Handle null flight (unselection)
    if (!flight) {
      setSelectedOutboundFlight(null);
      setShowPassengerDetails(false);
      return;
    }

    // Store current scroll position to prevent auto-scroll
    const currentScrollY = window.scrollY;

    // Select flight
    setSelectedOutboundFlight(flight);
    setShowPassengerDetails(true);

    // Prevent any automatic scrolling that might be triggered by DOM changes
    setTimeout(() => {
      if (window.scrollY !== currentScrollY) {
        window.scrollTo({ top: currentScrollY, behavior: 'instant' });
      }
    }, 0);
  };

  const handleOutboundFlightSelect = (flight) => {
    // Handle null flight (unselection)
    if (!flight) {
      setSelectedOutboundFlight(null);
      if (showPassengerDetails) {
        setShowPassengerDetails(false);
      }
      return;
    }

    // Store current scroll position to prevent auto-scroll
    const currentScrollY = window.scrollY;

    // Select flight
    setSelectedOutboundFlight(flight);

    // Prevent any automatic scrolling that might be triggered by DOM changes
    setTimeout(() => {
      if (window.scrollY !== currentScrollY) {
        window.scrollTo({ top: currentScrollY, behavior: 'instant' });
      }
    }, 0);
  };

  const handleReturnFlightSelect = (flight) => {
    // Handle null flight (unselection)
    if (!flight) {
      setSelectedReturnFlight(null);
      if (showPassengerDetails) {
        setShowPassengerDetails(false);
      }
      return;
    }

    // Store current scroll position to prevent auto-scroll
    const currentScrollY = window.scrollY;

    // Select flight
    setSelectedReturnFlight(flight);

    // Prevent any automatic scrolling that might be triggered by DOM changes
    setTimeout(() => {
      if (window.scrollY !== currentScrollY) {
        window.scrollTo({ top: currentScrollY, behavior: 'instant' });
      }
    }, 0);
  };

  const handleContinueToPassengerDetails = () => {
    if (tripType === 'return' && (!selectedOutboundFlight || !selectedReturnFlight)) {
      alert('Please select both outbound and return flights before continuing.');
      return;
    }

    // Show passenger details inline for return flights
    setShowPassengerDetails(true);
  };

  // Handle passenger details form submission - REDIRECT TO CHECKOUT
  const handlePassengerDetailsSubmit = (formData) => {
    console.log('🚀 HomePage: Passenger details submitted, redirecting to checkout');
    console.log('🚀 HomePage: Form data:', formData);

    // Save passenger data to booking context
    setPassengers(formData.passengers);
    setEmail(formData.email);
    setIsPassengerDetailsLoading(true);

    // Prepare checkout data for navigation state
    const checkoutData = {
      tripType: tripType,
      passengers: formData.passengers,
      email: formData.email,
      totalPrice: tripType === 'return' ? 9.98 : 4.99,
      searchData: searchData // Include search data for reference
    };

    // Add flight data based on trip type
    if (tripType === 'oneWay' && selectedOutboundFlight) {
      checkoutData.flight = {
        id: selectedOutboundFlight.id,
        airline: selectedOutboundFlight.airline,
        flightNumber: selectedOutboundFlight.flight?.number || selectedOutboundFlight.flightNumber,
        departure: selectedOutboundFlight.departure,
        arrival: selectedOutboundFlight.arrival,
        duration: selectedOutboundFlight.duration,
        price: selectedOutboundFlight.price
      };
    } else if (tripType === 'return') {
      if (selectedOutboundFlight) {
        checkoutData.outboundFlight = {
          id: selectedOutboundFlight.id,
          airline: selectedOutboundFlight.airline,
          flightNumber: selectedOutboundFlight.flight?.number || selectedOutboundFlight.flightNumber,
          departure: selectedOutboundFlight.departure,
          arrival: selectedOutboundFlight.arrival,
          duration: selectedOutboundFlight.duration,
          price: selectedOutboundFlight.price
        };
      }
      if (selectedReturnFlight) {
        checkoutData.returnFlight = {
          id: selectedReturnFlight.id,
          airline: selectedReturnFlight.airline,
          flightNumber: selectedReturnFlight.flight?.number || selectedReturnFlight.flightNumber,
          departure: selectedReturnFlight.departure,
          arrival: selectedReturnFlight.arrival,
          duration: selectedReturnFlight.duration,
          price: selectedReturnFlight.price
        };
      }
    }

    console.log('🚀 HomePage: Prepared checkout data:', checkoutData);

    // Save backup data to sessionStorage using helper
    try {
      const success = saveCheckoutData(checkoutData);
      if (success) {
        console.log('✅ HomePage: Backup data saved to sessionStorage');
      }
    } catch (error) {
      console.warn('⚠️ HomePage: Failed to save backup data:', error);
    }

    // Navigate to checkout page with state data
    setTimeout(() => {
      setIsPassengerDetailsLoading(false);
      console.log('✅ HomePage: Navigating to checkout with state');
      navigate('/checkout', {
        state: checkoutData
      });
    }, 200);
  };



  // Handle edit flight button click
  const handleEditFlight = () => {
    setShowPassengerDetails(false);
    // Scroll back to flight results
    if (resultsRef.current) {
      resultsRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Handle trip type changes
  const handleTripTypeChange = (newTripType) => {
    console.log('🔄 Trip type changing from', tripType, 'to', newTripType);
    setTripType(newTripType);

    // Clear return date if switching to one-way
    if (newTripType === 'oneWay') {
      setSearchData(prev => ({
        ...prev,
        returnDate: ''
      }));
    }

    // Clear flight selections when changing trip type
    setSelectedOutboundFlight(null);
    setSelectedReturnFlight(null);

    // Hide passenger details when changing trip type
    setShowPassengerDetails(false);

    // Clear results if they exist to avoid confusion
    if (showResults) {
      setShowResults(false);
      setHasSearched(false);
      setOutboundFlights([]);
      setReturnFlights([]);
      setOneWayFlights([]);
      setSearchError(null);
    }

    // Add debugging for return trip toggle
    console.log('🔄 Trip type after change:', newTripType);
    console.log('🔄 Current tripType state will be:', newTripType);
    setTimeout(() => {
      console.log('🔄 Actual tripType state after setState:', tripType);
    }, 100);
  };

  // Function to scroll back to search form
  const scrollToSearchForm = () => {
    searchFormRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  };



  // Get tomorrow's date as minimum date
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split('T')[0];

  // Get next week's date for return date
  const nextWeek = new Date();
  nextWeek.setDate(nextWeek.getDate() + 7);
  const defaultReturnDate = nextWeek.toISOString().split('T')[0];

  return (
    <div className="min-h-screen">


      {/* Premium Floating CTA */}
      <FloatingCTA onScrollToForm={scrollToSearchForm} />


      {/* Edit Mode Message */}
      {isEditMode && editModeMessage && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-accent-500 to-accent-600 text-white py-4 px-6 shadow-aviation"
        >
          <div className="max-w-6xl mx-auto flex items-center justify-between">
            <div className="flex items-center">
              <svg className="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <span className="font-semibold">{editModeMessage}</span>
            </div>
            <button
              onClick={() => {
                setIsEditMode(false);
                setEditModeMessage('');
              }}
              className="text-white/80 hover:text-white transition-colors"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </motion.div>
      )}

      {/* £20,000+ Luxury Aviation Hero Section */}
      <section className="relative min-h-screen flex items-center gradient-luxury-hero overflow-x-hidden" style={{ paddingTop: isEditMode ? '80px' : '0' }}>
        {/* Luxury Aviation Background System */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/12 via-blue-500/8 to-accent-500/12"></div>

        {/* Advanced Animated Aviation Elements */}
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-brand-400/20 to-brand-600/10 rounded-full blur-3xl animate-luxury-float"></div>
        <div className="absolute bottom-20 right-10 w-[32rem] h-[32rem] bg-gradient-to-br from-accent-400/20 to-accent-600/10 rounded-full blur-3xl animate-luxury-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[700px] h-[700px] bg-gradient-to-r from-brand-300/15 to-accent-300/15 rounded-full blur-3xl animate-luxury-float" style={{animationDelay: '4s'}}></div>
        <div className="absolute top-1/3 right-1/4 w-80 h-80 bg-gradient-to-br from-blue-400/15 to-purple-400/10 rounded-full blur-2xl animate-luxury-float" style={{animationDelay: '6s'}}></div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(14 165 233) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>

        <div className="container-modern text-center relative z-10 pt-24 pb-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Consolidated Auto-Scrolling Trust Strip - All 7 Badges */}
            <div className="relative mb-8 overflow-hidden">
              <div className="flex animate-scroll-horizontal space-x-6 whitespace-nowrap">

                {/* Professional Quality Guarantee */}
                <div className="inline-flex items-center bg-purple-50 text-purple-700 px-4 py-2 rounded-full text-sm font-semibold border border-purple-200 flex-shrink-0">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>Professional Quality Guarantee</span>
                </div>
                {/* 75,000+ Happy Travelers */}
                <div className="inline-flex items-center bg-indigo-50 text-indigo-700 px-4 py-2 rounded-full text-sm font-semibold border border-indigo-200 flex-shrink-0">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <span><AnimatedCounter end={75000} suffix="+ Happy Travelers" /></span>
                </div>
                {/* 24/7 Customer Support */}
                <div className="inline-flex items-center bg-teal-50 text-teal-700 px-4 py-2 rounded-full text-sm font-semibold border border-teal-200 flex-shrink-0">
                  <div className="w-2 h-2 bg-teal-500 rounded-full animate-pulse mr-2"></div>
                  <span>24/7 Customer Support</span>
                </div>
                {/* 60 Second Delivery */}
                <div className="inline-flex items-center bg-green-50 text-green-700 px-4 py-2 rounded-full text-sm font-semibold border border-green-200 flex-shrink-0">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                  </svg>
                  <span>60 Second Delivery</span>
                </div>
                {/* Duplicate badges for seamless loop */}

                {/* Professional Quality Guarantee */}
                <div className="inline-flex items-center bg-purple-50 text-purple-700 px-4 py-2 rounded-full text-sm font-semibold border border-purple-200 flex-shrink-0">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>Professional Quality Guarantee</span>
                </div>
                {/* 75,000+ Happy Travelers */}
                <div className="inline-flex items-center bg-indigo-50 text-indigo-700 px-4 py-2 rounded-full text-sm font-semibold border border-indigo-200 flex-shrink-0">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <span><AnimatedCounter end={75000} suffix="+ Happy Travelers" /></span>
                </div>
                {/* 24/7 Customer Support */}
                <div className="inline-flex items-center bg-teal-50 text-teal-700 px-4 py-2 rounded-full text-sm font-semibold border border-teal-200 flex-shrink-0">
                  <div className="w-2 h-2 bg-teal-500 rounded-full animate-pulse mr-2"></div>
                  <span>24/7 Customer Support</span>
                </div>
                {/* 60 Second Delivery */}
                <div className="inline-flex items-center bg-green-50 text-green-700 px-4 py-2 rounded-full text-sm font-semibold border border-green-200 flex-shrink-0">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                  </svg>
                  <span>60 Second Delivery</span>
                </div>
              </div>
            </div>

            {/* Enhanced Professional Aviation Headline - Better Hierarchy & Balance */}
            <motion.div
              className="mb-16"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1.2, ease: "easeOut" }}
            >
              <div className="text-center">
                <div className="mb-8">
                  <h1 className="heading-hero mb-8">
                    <span className="block text-neutral-800 mb-2">
                      Professional Flight
                    </span>
                    <span className="block text-neutral-700 mb-2">
                      Reservations for Visas,
                    </span>
                    <span className="block text-gradient-hero">
                      Travel & Immigration
                    </span>
                  </h1>

                  {/* Ultra-Premium Aviation Pricing Section */}
                  <motion.div
                    initial={{ opacity: 0, y: 40 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.3 }}
                    className="max-w-5xl mx-auto mb-16"
                  >


                    {/* Compact Pricing Cards */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12 px-4 py-4">
                      {/* One-Way Compact Card */}
                      <motion.div
                        initial={{ opacity: 0, x: -40 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.7, delay: 0.7 }}
                        className="card-luxury group relative overflow-hidden min-h-[280px] flex flex-col shadow-pricing-deep hover:shadow-pricing-deep-hover transition-all duration-500 hover:-translate-y-4 hover:scale-[1.02]"
                      >
                        {/* Premium aviation shimmer effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-brand-100/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1200"></div>

                        {/* Card Content */}
                        <div className="relative z-10 p-8 flex flex-col h-full">
                          {/* Icon Section */}
                          <div className="flex justify-center mb-6">
                            <div className="w-16 h-16 bg-gradient-to-br from-brand-500 via-brand-600 to-brand-700 rounded-2xl flex items-center justify-center shadow-aviation group-hover:scale-110 group-hover:rotate-3 transition-all duration-500">
                              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                              </svg>
                            </div>
                          </div>

                          {/* Pricing Section */}
                          <div className="text-center flex-grow">
                            <div className="text-4xl md:text-5xl font-black bg-gradient-to-r from-brand-600 via-brand-700 to-brand-800 bg-clip-text text-transparent mb-3 leading-none">
                              $4.99
                            </div>
                            <h3 className="text-xl font-bold text-brand-700 mb-3">One-Way Reservation</h3>
                            <p className="text-sm text-neutral-700 leading-relaxed max-w-sm mx-auto font-medium">
                              Embassy-approved flight reservation for single destination visa applications with verifiable booking reference
                            </p>
                          </div>
                        </div>
                      </motion.div>

                      {/* Return Journey Compact Card */}
                      <motion.div
                        initial={{ opacity: 0, x: 40 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.7, delay: 0.8 }}
                        className="card-luxury group relative min-h-[280px] flex flex-col border-2 border-accent-200 shadow-pricing-deep hover:shadow-pricing-deep-hover transition-all duration-500 hover:-translate-y-4 hover:scale-[1.02]"
                        style={{ overflow: 'visible' }}
                      >
                        {/* Premium aviation shimmer effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-accent-100/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1200 rounded-3xl"></div>

                        {/* Most Popular Badge - Fixed positioning */}
                        <div className="absolute -top-3 -right-3 z-30">
                          <div className="bg-gradient-to-r from-accent-500 via-accent-600 to-accent-700 text-white px-4 py-2 rounded-2xl text-xs font-bold shadow-aviation transform rotate-12 border-2 border-white whitespace-nowrap">
                            ⭐ Most Popular
                          </div>
                        </div>

                        {/* Card Content */}
                        <div className="relative z-10 p-8 flex flex-col h-full">
                          {/* Icon Section */}
                          <div className="flex justify-center mb-6">
                            <div className="w-16 h-16 bg-gradient-to-br from-accent-500 via-accent-600 to-accent-700 rounded-2xl flex items-center justify-center shadow-aviation group-hover:scale-110 group-hover:rotate-3 transition-all duration-500">
                              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                              </svg>
                            </div>
                          </div>

                          {/* Pricing Section */}
                          <div className="text-center flex-grow">
                            <div className="text-4xl md:text-5xl font-black bg-gradient-to-r from-accent-600 via-accent-700 to-accent-800 bg-clip-text text-transparent mb-3 leading-none">
                              $9.98
                            </div>
                            <h3 className="text-xl font-bold text-accent-700 mb-3">Return Journey</h3>
                            <p className="text-sm text-neutral-700 leading-relaxed max-w-sm mx-auto font-medium">
                              Complete round-trip reservation package for comprehensive visa applications with both flight segments
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    </div>

                  </motion.div>
                </div>

              </div>
            </motion.div>

          {/* Premium Value Proposition */}
          <div className="max-w-5xl mx-auto mb-16">
            <div className="card-premium p-8 md:p-12">
              <p className="text-xl md:text-2xl text-neutral-700 leading-relaxed mb-8 font-medium">
                <strong className="text-brand-700">Professional onward flight reservations designed for every traveler's need</strong> — whether you're applying for visas, meeting strict border entry requirements, planning flexible trips, or securing digital nomad visas. Our embassy-approved documents come with verifiable booking references and are trusted by visa officers at 195+ embassies worldwide, helping you travel confidently and stress-free.
              </p>


              </div>
            </div>



          {/* Premium Aviation Search Form */}
          <div className="px-4 py-8">
            <motion.div
              id="search-form"
              ref={searchFormRef}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="card-aviation max-w-6xl mx-auto relative overflow-visible"
            >
            {/* Premium aviation shimmer effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-brand-100/30 to-transparent -translate-x-full animate-shimmer rounded-3xl overflow-hidden pointer-events-none"></div>

            {/* Enhanced Form Header with Progress Indicator */}
            <div className="text-center mb-10 relative z-30">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-500 to-brand-600 rounded-2xl flex items-center justify-center shadow-aviation">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                  </svg>
                </div>
                <h3 className="heading-secondary text-brand-700">
                  Find Your Perfect Flight
                </h3>
              </div>
              <p className="text-body-large text-neutral-600 max-w-2xl mx-auto mb-6">
                Professional flight reservations trusted by <strong className="text-brand-700"><AnimatedCounter end={195} suffix="+ embassies" /></strong> worldwide.
              </p>

              {/* Progress Steps Indicator */}
              <div className="flex items-center justify-center space-x-4 mb-6">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-brand-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                  <span className="text-sm font-semibold text-brand-700">Search Flights</span>
                </div>
                <div className="w-8 h-0.5 bg-neutral-200"></div>
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-neutral-200 text-neutral-400 rounded-full flex items-center justify-center text-sm font-bold">2</div>
                  <span className="text-sm font-medium text-neutral-400">Select & Pay</span>
                </div>
                <div className="w-8 h-0.5 bg-neutral-200"></div>
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-neutral-200 text-neutral-400 rounded-full flex items-center justify-center text-sm font-bold">3</div>
                  <span className="text-sm font-medium text-neutral-400">Download</span>
                </div>
              </div>


            </div>
            <form onSubmit={handleSearch} className="space-y-8 px-8 md:px-12">
              {/* Premium Aviation Trip Type Toggle */}
              <div className="flex justify-center mb-10">
                <div className="flex bg-brand-50 rounded-2xl p-2 shadow-aviation border border-brand-200">
                  <motion.button
                    type="button"
                    onClick={() => handleTripTypeChange('oneWay')}
                    className={`px-8 py-4 rounded-xl font-bold transition-all duration-300 transform ${
                      tripType === 'oneWay'
                        ? 'bg-brand-500 text-white shadow-aviation scale-105'
                        : 'text-brand-600 hover:text-brand-700 hover:bg-white hover:shadow-soft'
                    }`}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                      </svg>
                      <span>One Way</span>
                    </div>
                  </motion.button>
                  <motion.button
                    type="button"
                    onClick={() => handleTripTypeChange('return')}
                    className={`px-8 py-4 rounded-xl font-bold transition-all duration-300 transform ${
                      tripType === 'return'
                        ? 'bg-brand-500 text-white shadow-aviation scale-105'
                        : 'text-brand-600 hover:text-brand-700 hover:bg-white hover:shadow-soft'
                    }`}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                      </svg>
                      <span>Return</span>
                    </div>
                  </motion.button>
                </div>
              </div>

              {/* Validation Summary */}
              <ValidationSummary errors={validationErrors} />



              {/* Premium Aviation Input Grid */}
              <div className="flight-inputs-container space-y-8">
                {/* First Row: Origin and Destination */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Premium Origin Airport */}
                  <div className="min-w-0">
                    <label className="block text-base font-bold text-brand-700 mb-4 flex items-center">
                      <div className="w-8 h-8 bg-brand-100 rounded-xl flex items-center justify-center mr-3">
                        <svg className="w-4 h-4 text-brand-600" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                        </svg>
                      </div>
                      Departure Airport
                    </label>
                    <div className="relative">
                      <AirportAutocomplete
                        value={searchData.origin}
                        onChange={(value) => handleInputChange('origin', value)}
                        placeholder="Search departure city or airport (e.g., London, LHR)"
                        hasError={!!validationErrors.origin}
                      />
                      {validationErrors.origin && (
                        <p className="mt-3 text-sm text-red-600 font-semibold flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {validationErrors.origin}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Premium Destination Airport */}
                  <div className="min-w-0">
                    <label className="block text-base font-bold text-accent-700 mb-4 flex items-center">
                      <div className="w-8 h-8 bg-accent-100 rounded-xl flex items-center justify-center mr-3">
                        <svg className="w-4 h-4 text-accent-600" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M2.5 19h19v2h-19v-2zm1.57-2h17.86c.22 0 .42-.15.47-.37l.8-3.2c.08-.33-.18-.63-.53-.63H16.5l.5-2h2.17c.22 0 .42-.15.47-.37l.8-3.2c.08-.33-.18-.63-.53-.63H3.09c-.35 0-.61.3-.53.63l.8 3.2c.***********.47.37H6l.5 2H2.33c-.35 0-.61.3-.53.63l.8 3.2c.***********.47.37z"/>
                        </svg>
                      </div>
                      Arrival Airport
                    </label>
                    <div className="relative">
                      <AirportAutocomplete
                        value={searchData.destination}
                        onChange={(value) => handleInputChange('destination', value)}
                        placeholder="Search destination city or airport (e.g., New York, JFK)"
                        hasError={!!validationErrors.destination}
                      />
                      {validationErrors.destination && (
                        <p className="mt-3 text-sm text-red-600 font-semibold flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {validationErrors.destination}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Second Row: Premium Date Inputs */}
                <div className={`grid gap-8 ${
                  tripType === 'return'
                    ? 'grid-cols-1 lg:grid-cols-2' // Return: Stack on mobile, side-by-side on larger screens
                    : 'grid-cols-1 lg:grid-cols-2' // One-way: Consistent layout
                }`}>
                  {/* Premium Departure Date */}
                  <div className="min-w-0">
                    <label className="block text-base font-bold text-brand-700 mb-4 flex items-center">
                      <div className="w-8 h-8 bg-brand-100 rounded-xl flex items-center justify-center mr-3">
                        <svg className="w-4 h-4 text-brand-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                        </svg>
                      </div>
                      Departure Date
                    </label>
                    <input
                      type="date"
                      value={searchData.date}
                      onChange={(e) => {
                        console.log('📅 Date input changed:', e.target.value);
                        handleInputChange('date', e.target.value);
                      }}
                      onClick={(e) => {
                        console.log('📅 Date input clicked');
                        e.target.showPicker && e.target.showPicker();
                      }}
                      min={minDate}
                      className={`input-aviation text-lg ${
                        validationErrors.date ? 'border-red-500 focus:ring-red-500' : ''
                      }`}
                      required
                    />
                    {validationErrors.date && (
                      <p className="mt-3 text-sm text-red-600 font-semibold flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {validationErrors.date}
                      </p>
                    )}
                  </div>

                  {/* Premium Return Date - Only show when return trip is selected */}
                  {(() => {
                    console.log('🔄 Rendering return date field check - tripType:', tripType, 'should show:', tripType === 'return');
                    return tripType === 'return';
                  })() && (
                    <div className="min-w-0">
                      <label className="block text-base font-bold text-accent-700 mb-4 flex items-center">
                        <div className="w-8 h-8 bg-accent-100 rounded-xl flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-accent-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                          </svg>
                        </div>
                        Return Date
                      </label>
                      <input
                        type="date"
                        value={searchData.returnDate}
                        onChange={(e) => {
                          console.log('🔄 Return date input changed:', e.target.value);
                          handleInputChange('returnDate', e.target.value);
                        }}
                        onClick={(e) => {
                          console.log('🔄 Return date input clicked');
                          e.target.showPicker && e.target.showPicker();
                        }}
                        min={searchData.date || minDate}
                        className={`input-aviation text-lg ${
                          validationErrors.returnDate ? 'border-red-500 focus:ring-red-500' : ''
                        }`}
                        required
                      />
                      {validationErrors.returnDate && (
                        <p className="mt-3 text-sm text-red-600 font-semibold flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {validationErrors.returnDate}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>



              {/* Enhanced CTA Section with Consistent Styling */}
              <div className="space-y-8 pt-4">
                <motion.button
                  type="submit"
                  disabled={isLoading}
                  className="w-full btn-cta-primary btn-cta-large disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {/* Enhanced button shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                  {isLoading ? (
                    <div className="flex items-center justify-center relative z-10">
                      <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent mr-3"></div>
                      <span className="font-bold">Finding Perfect Flights...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center relative z-10">
                      <svg className="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                      </svg>
                      <span className="font-bold">Search Premium Flights</span>
                      <svg className="w-5 h-5 ml-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </motion.button>


              </div>



            </form>

          </motion.div>
          </div>


        </motion.div>
        </div>
      </section>

      {/* Inline Flight Results Section */}
      <AnimatePresence>
        {(showResults || hasSearched) && (
          <section ref={resultsRef} className="section-padding bg-neutral-50">
            <div className="container-modern">
              <InlineFlightResults
                isLoading={isLoading}
                searchError={searchError}
                tripType={tripType}
                oneWayFlights={oneWayFlights}
                outboundFlights={outboundFlights}
                returnFlights={returnFlights}
                selectedOutboundFlight={selectedOutboundFlight}
                selectedReturnFlight={selectedReturnFlight}
                onOneWayFlightSelect={handleOneWayFlightSelect}
                onOutboundFlightSelect={handleOutboundFlightSelect}
                onReturnFlightSelect={handleReturnFlightSelect}
                onContinue={handleContinueToPassengerDetails}
                searchData={searchData}
                onModifySearch={() => {
                  // Clear current flight selections based on trip type
                  if (tripType === 'oneWay') {
                    setSelectedOutboundFlight(null);
                  } else if (tripType === 'return') {
                    setSelectedOutboundFlight(null);
                    setSelectedReturnFlight(null);
                  }

                  // Hide Flight Summary and Passenger Details sections
                  setShowPassengerDetails(false);

                  // Clear any search errors
                  setSearchError(null);

                  // Show the flight results section again (preserve search results)
                  // Keep showResults and hasSearched as true to display available flights
                  // This allows users to see the flight options again without re-searching

                  // Scroll back to flight results section for smooth UX
                  if (resultsRef.current) {
                    resultsRef.current.scrollIntoView({
                      behavior: 'smooth',
                      block: 'start'
                    });
                  }
                }}
                showPassengerDetails={showPassengerDetails}
                passengers={passengers}
                email={email}
                onPassengerDetailsSubmit={handlePassengerDetailsSubmit}
                isPassengerDetailsLoading={isPassengerDetailsLoading}
                enableInlinePayment={false}
              />
            </div>
          </section>
        )}
      </AnimatePresence>

      {/* Use Cases Section */}
      <section className="section-rhythm bg-gradient-to-br from-neutral-50 to-brand-50/30" aria-labelledby="use-cases-heading">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-6 py-3 rounded-full text-sm font-semibold mb-8">
              <span className="w-2 h-2 bg-brand-500 rounded-full mr-3 animate-pulse"></span>
              USE CASES
            </div>
            <h2 id="use-cases-heading" className="text-4xl md:text-5xl font-black text-brand-800 mb-6 leading-tight">
              When you might need
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent">our service</span>
            </h2>
            <p className="text-xl text-brand-700 leading-relaxed font-medium max-w-3xl mx-auto">
              From visa applications to flexible travel plans, discover how VerifiedOnward helps travelers in various situations worldwide.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
            {[
              {
                title: "Visa Applications",
                subtitle: "Schengen, US, UK & More",
                description: "Embassy-approved documentation for visa applications worldwide. Accepted by 195+ embassies.",
                icon: (
                  <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                    <path d="M8,12V14H16V12H8M8,16V18H13V16H8Z" />
                  </svg>
                ),
                bgColor: "bg-gradient-to-br from-brand-50 to-brand-100",
                borderColor: "border-brand-200 hover:border-brand-400",
                iconColor: "text-brand-600",
                hoverBg: "group-hover:from-brand-100 group-hover:to-brand-150"
              },
              {
                title: "Digital Nomad Visa",
                subtitle: "Remote Work & Long-term Stay",
                description: "Perfect for remote workers and digital nomads needing proof of onward travel for visa applications.",
                icon: (
                  <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14,6H20V16H16V18A2,2 0 0,1 14,20H4A2,2 0 0,1 2,18V8A2,2 0 0,1 4,6H6V4A2,2 0 0,1 8,2H12A2,2 0 0,1 14,4V6M4,8V18H14V8H4M8,4V6H12V4H8Z" />
                  </svg>
                ),
                bgColor: "bg-gradient-to-br from-green-50 to-green-100",
                borderColor: "border-green-200 hover:border-brand-400",
                iconColor: "text-green-600",
                hoverBg: "group-hover:from-green-100 group-hover:to-green-150"
              },
              {
                title: "Flexible Travel Plans",
                subtitle: "Spontaneous & Open-ended",
                description: "Keep your options open with flexible reservations that don't lock you into rigid travel schedules.",
                icon: (
                  <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z" />
                  </svg>
                ),
                bgColor: "bg-gradient-to-br from-purple-50 to-purple-100",
                borderColor: "border-purple-200 hover:border-brand-400",
                iconColor: "text-purple-600",
                hoverBg: "group-hover:from-purple-100 group-hover:to-purple-150"
              },
              {
                title: "Border Entry Requirements",
                subtitle: "Immigration & Customs",
                description: "Meet strict border entry requirements with professional documentation accepted by immigration worldwide.",
                icon: (
                  <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.4 16,13V16C16,17.4 15.4,18 14.8,18H9.2C8.6,18 8,17.4 8,16V13C8,12.4 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z" />
                  </svg>
                ),
                bgColor: "bg-gradient-to-br from-accent-50 to-accent-100",
                borderColor: "border-accent-200 hover:border-brand-400",
                iconColor: "text-accent-600",
                hoverBg: "group-hover:from-accent-100 group-hover:to-accent-150"
              }
            ].map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                whileHover={{ y: -8, scale: 1.02 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`${useCase.bgColor} ${useCase.hoverBg} rounded-3xl p-6 md:p-8 border-2 ${useCase.borderColor} shadow-aviation hover:shadow-aviation-hover transition-all duration-500 group cursor-pointer relative overflow-hidden`}
              >
                {/* Enhanced hover effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  <div className={`w-20 h-20 ${useCase.bgColor} ${useCase.iconColor} rounded-3xl flex items-center justify-center mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-soft`}>
                    {useCase.icon}
                  </div>
                  <h3 className="heading-tertiary mb-3 text-neutral-900 group-hover:text-neutral-900 transition-colors duration-300">{useCase.title}</h3>
                  <p className={`text-sm font-bold ${useCase.iconColor} mb-4 uppercase tracking-wide`}>{useCase.subtitle}</p>
                  <p className="text-body text-neutral-700 group-hover:text-neutral-800 transition-colors duration-300 font-medium">{useCase.description}</p>

                  {/* Enhanced visual indicator */}
                  <div className="mt-6 flex items-center text-sm font-semibold text-neutral-500 group-hover:text-neutral-700 transition-colors duration-300">
                    <span>Learn more</span>
                    <svg className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center mt-12"
          >
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                to="/use-cases"
                className="inline-flex items-center bg-brand-500 hover:bg-brand-600 text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300 shadow-aviation hover:shadow-aviation-hover transform hover:scale-105 relative overflow-hidden group"
              >
                {/* Enhanced shimmer effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                <div className="relative z-10 flex items-center">
                <span>View All Use Cases</span>
                <svg className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                </div>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>





      {/* Testimonials Section */}
      <Testimonials />



    </div>
  );
};

export default HomePage;
