import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';

const HomepageFAQ = () => {
  const [openIndex, setOpenIndex] = useState(null);

  const faqs = [
    {
      question: "What are embassy-approved flight reservations?",
      answer: "VerifiedOnward provides professional airline reservations with verifiable booking references for visa applications. These are authentic flight reservations that meet embassy requirements and are trusted by immigration offices in 195+ countries worldwide. They show your travel itinerary without requiring you to purchase expensive, non-refundable tickets that could cost $500+."
    },
    {
      question: "How do I get my embassy-approved flight reservation?",
      answer: "Our streamlined 3-step process takes just 60 seconds: 1. Search for flights using our real-time airline data system. 2. Enter passenger details (up to 2 travelers per reservation). 3. Pay the one-time fee of $4.99 via secure payment. 4. Instantly download your professional, embassy-ready reservation document."
    },
    {
      question: "Will my flight reservation be accepted by all embassies?",
      answer: "Yes! Our flight reservations are designed to meet the requirements of embassies and consulates in 195+ countries worldwide. We use authentic airline data and professional formatting that matches real airline reservations, ensuring embassy acceptance for visa applications."
    },
    {
      question: "Does my reservation show a real flight itinerary?",
      answer: "Absolutely! Your VerifiedOnward reservation displays authentic flight data from global airline systems, including airline name, flight number, departure/arrival times, aircraft type, and duration. The result is a professional, embassy-approved flight reservation with verifiable booking reference that meets all visa application requirements."
    },
    {
      question: "Can I add multiple passengers to my reservation?",
      answer: "Yes! You can add up to 2 passengers to a single flight reservation at no extra cost. Simply enter their names during booking and all travelers will appear on one shared embassy-approved reservation document. This is perfect for couples, families, or business partners applying for visas together."
    },
    {
      question: "How long is my flight reservation valid?",
      answer: "Your flight reservation remains valid for the duration shown on the document, typically 24-48 hours from the time of creation. This provides sufficient time for your visa application submission. If you need a longer validity period for your specific embassy requirements, please contact our support team."
    }
  ];

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="section-rhythm bg-gradient-to-br from-white to-brand-50/30" aria-labelledby="faq-heading">
      {/* Premium Aviation Background Elements */}
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-brand-500/5 to-accent-500/5"></div>
      <div className="absolute top-20 right-20 w-64 h-64 bg-brand-400/8 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-20 left-20 w-80 h-80 bg-accent-400/8 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>

      <div className="container-modern relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center bg-brand-100 text-brand-700 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            Most Common Questions Answered
          </div>

          <h2 id="faq-heading" className="text-4xl md:text-5xl font-black text-brand-800 mb-6 leading-tight">
            Get Your{' '}
            <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent">
              Answers Instantly
            </span>
          </h2>
          <p className="text-xl text-brand-700 leading-relaxed font-medium max-w-3xl mx-auto">
            Everything you need to know about our professional, embassy-approved flight reservation service.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto space-y-6 mb-12">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white/90 backdrop-blur-md rounded-3xl border-2 border-brand-200 shadow-aviation hover:shadow-aviation-hover transition-all duration-300 group relative overflow-hidden"
            >
              {/* Premium gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 to-accent-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              <div className="relative z-10">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-white/50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-inset group-hover:bg-white/30"
                  aria-expanded={openIndex === index}
                  aria-controls={`faq-answer-${index}`}
                >
                  <h3 className="text-xl font-bold text-neutral-900 pr-4 group-hover:text-brand-600 transition-colors">
                    {faq.question}
                  </h3>
                  <div className="flex-shrink-0">
                    <motion.div
                      animate={{ rotate: openIndex === index ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                      className="w-8 h-8 bg-gradient-to-r from-brand-500 to-accent-500 rounded-full flex items-center justify-center shadow-soft group-hover:shadow-aviation transition-all duration-300"
                    >
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </motion.div>
                  </div>
                </button>
                
                <AnimatePresence>
                  {openIndex === index && (
                    <motion.div
                      id={`faq-answer-${index}`}
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="overflow-hidden"
                    >
                      <div className="px-8 pb-6 pt-2">
                        <p className="text-neutral-700 leading-relaxed font-medium">
                          {faq.answer}
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          ))}
        </div>

        {/* View All FAQs Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center"
        >
          <motion.div
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            <Link
              to="/faq"
              className="inline-flex items-center bg-brand-500 hover:bg-brand-600 text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300 shadow-aviation hover:shadow-aviation-hover transform hover:scale-105 relative overflow-hidden group"
            >
              {/* Enhanced shimmer effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              <div className="relative z-10 flex items-center">
                <span>View All FAQs</span>
                <svg className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default HomepageFAQ;
